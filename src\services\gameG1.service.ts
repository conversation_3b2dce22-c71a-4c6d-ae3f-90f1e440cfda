import { AppDataSource } from '@/config/config'
import { UserSpin, LuckyPrize, SpinHistory, TblUsers, TblGames, VoucherDraw } from '@/entities'
import { AuthorizedUserRequest } from '@/middlewares/auth'
import { luckyWheelService, redisService } from '@/services'
import { NotFoundError } from '@/utils/ApiError'
import moment from 'moment'

// Helper functions
const userSpinRepo = () => AppDataSource.getRepository(UserSpin)
const prizeRepo = () => AppDataSource.getRepository(LuckyPrize)
const spinHistoryRepo = () => AppDataSource.getRepository(SpinHistory)
const gameRepo = () => AppDataSource.getRepository(TblGames)
const voucherDrawRepo = () => AppDataSource.getRepository(VoucherDraw)
const userRepo = () => AppDataSource.getRepository(TblUsers)

// Helper function to get current time in GMT+7
const getNowGMT7 = () => {
  return moment.utc().add(7, 'hours').toDate();
}

// Helper function to convert gameId slug to number
export const convertGameIdSlugToNumber = (gameIdSlug: string): number => {
  switch (gameIdSlug) {
    case 'g1':
      return 27;
    case 'g2':
      return 28;
    // Thêm các case khác nếu cần
    default:
      return parseInt(gameIdSlug) || 0
  }
}

// Helper function to check if it's same day (Vietnam timezone)
const isSameDay = (date1: Date, date2: Date): boolean => {
  const d1 = new Date(date1.getTime() - (7 * 3600000)) // Convert to GMT+7
  const d2 = new Date(date2.getTime() - (7 * 3600000)) // Convert to GMT+7
  return d1.toDateString() === d2.toDateString()
}

// Helper function to filter prizes that can be randomly selected
const getEligiblePrizesForRandom = (prizes: any[]) => {
  const eligible = prizes.filter(prize => 
    prize.type === 'lose' || // Luôn có thể chọn lose prizes
    (['voucher', 'spin'].includes(prize.type) && prize.remainingQuantity > 0) // Chỉ chọn voucher còn số lượng
  )
  return eligible
}

// Enhanced randomPrize function that respects remaining quantity and winrate
const randomPrizeWithQuantityCheck = (prizes: any[]): any | null => {
  const eligiblePrizes = getEligiblePrizesForRandom(prizes)
  
  if (eligiblePrizes.length === 0) {
    return null
  }
  
  // Tính tổng winrate của các prizes còn lại
  let total = eligiblePrizes.reduce((sum, p) => sum + (p.winrate || 0), 0)
  console.log('total', total);
  console.log('eligiblePrizes', eligiblePrizes);
  if (total === 0) {
    // Nếu không có winrate nào được set, chọn random đều
    const randomIndex = Math.floor(Math.random() * eligiblePrizes.length)
    return eligiblePrizes[randomIndex]
  }

  // Nếu total < 100 thì kiểm tra các tổng winrate của các prize có type = voucher và set lại winrate cho các prize có type = lose
  if (total < 100) {
    const voucherPrizes = eligiblePrizes.filter(p => p.type === 'voucher')
    const losePrizes = eligiblePrizes.filter(p => p.type === 'lose')
    
    if (voucherPrizes.length > 0) {
        const totalVoucherWinrate = voucherPrizes.reduce((sum, p) => sum + (p.winrate || 0), 0)
        const remainingWinrate = 100 - totalVoucherWinrate
        if (losePrizes.length > 0) {
          const newLoseWinrate = remainingWinrate / losePrizes.length;
          // cập nhật lại total và eligiblePrizes theo thay đổi winrate của lose prizes
          eligiblePrizes.forEach(p => {
            if (p.type === 'lose') {
              p.winrate = newLoseWinrate
            }
          })
        }
        console.log('losePrizes', losePrizes)
    }
    
    total = eligiblePrizes.reduce((sum, p) => sum + (p.winrate || 0), 0)
  }
  
  // Random theo tỉ lệ winrate
  let rand = Math.random() * total
  
  for (const prize of eligiblePrizes) {
    const prizeWinrate = prize.winrate || 0
    if (rand < prizeWinrate) {
      return prize
    }
    rand -= prizeWinrate
  }
  
  // Fallback: trả về prize cuối cùng nếu có lỗi tính toán
  return eligiblePrizes[eligiblePrizes.length - 1]
}

// Helper function to get prizes with remaining quantity (OPTIMIZED with CACHING)
const getPrizesWithRemainingQuantity = async (campaignId: number, gameId: number, context: string) => {
  // OPTIMIZATION: Cache key cho won counts
  const cacheKey = `prize_won_counts:${campaignId}:${gameId}:${context || '0'}`

  let prizes = [];
  if(!context || context == '' || context == '0') {
    prizes = await luckyWheelService.getActivePrizesNotContext(campaignId, gameId)
  }else{
    prizes = await luckyWheelService.getActivePrizesContext(campaignId, gameId, context)
  }

  // OPTIMIZATION: Batch query để lấy số lượng đã trúng cho tất cả prizes cùng lúc
  const voucherPrizeIds = prizes
    .filter(prize => ['voucher', 'spin'].includes(prize.type) && prize.bizStorageId)
    .map(prize => prize.id)

  const wonCountMap = new Map<number, number>()

  if (voucherPrizeIds.length > 0) {
    // Kiểm tra cache trước
    let cachedWonCounts: any = null
    try {
      const cached = await redisService.Shared.getClient().get(cacheKey)
      if (cached) {
        cachedWonCounts = JSON.parse(cached)
      }
    } catch (error) {
      console.log('Redis cache error:', error)
    }

    if (cachedWonCounts) {
      // Sử dụng data từ cache
      Object.entries(cachedWonCounts).forEach(([prizeId, count]) => {
        wonCountMap.set(parseInt(prizeId), count as number)
      })
    } else {
      // Single query để lấy tất cả won counts thay vì N queries riêng lẻ
      const wonCounts = await spinHistoryRepo()
        .createQueryBuilder('spin_history')
        .select('spin_history.prizeId', 'prizeId')
        .addSelect('COUNT(*)', 'count')
        .where('spin_history.prizeId IN (:...prizeIds)', { prizeIds: voucherPrizeIds })
        .groupBy('spin_history.prizeId')
        .getRawMany()

      // Tạo map để lookup nhanh O(1)
      const cacheData: any = {}
      wonCounts.forEach(item => {
        const prizeId = parseInt(item.prizeId)
        const count = parseInt(item.count)
        wonCountMap.set(prizeId, count)
        cacheData[prizeId] = count
      })

      // Cache kết quả trong 30 giây
      try {
        await redisService.Shared.getClient().setex(cacheKey, 30, JSON.stringify(cacheData))
      } catch (error) {
        console.log('Redis cache set error:', error)
      }
    }
  }

  // Tính remainingQuantity cho từng prize (không cần Promise.all vì không có async operations)
  const prizesWithRemaining = prizes.map(prize => {
    let remainingQuantity = prize.quantity

    // Chỉ tính số lượng đã trúng cho voucher
    if (['voucher', 'spin'].includes(prize.type) && prize.bizStorageId) {
      const wonCount = wonCountMap.get(prize.id) || 0
      remainingQuantity = prize.quantity - wonCount
    }

    return {
      ...prize,
      remainingQuantity
    }
  })

  return prizesWithRemaining
}

// Module xử lý coupon từ Bizfly
interface CouponData {
  couponId: number
  couponCode: string
  couponName: string
  qrCodeLink: string
}

interface CouponResult {
  success: boolean
  couponData?: CouponData
  replacementPrize?: any
  message: string
}

const processCouponFromBizfly = async (
  user: any,
  prize: any,
  prizes: any[],
  game: any,
  campaignId: number,
  gameId: number
): Promise<CouponResult> => {
  if (!prize.bizStorageId) {
    return {
      success: false,
      message: prize.name
    }
  }

  try {
    // Kiểm tra remainingQuantity đã được tính sẵn
    if (prize.remainingQuantity > 0) {
      const couponResponse = await luckyWheelService.getCouponFromBizfly(
        user.id,
        prize.bizStorageId,
        user.tokyoId,
        user.bizId,
        game.name,
        campaignId,
        gameId,
        4
      )

      if (couponResponse && couponResponse.data) {
        const couponData: CouponData = {
          couponId: couponResponse.data.coupon_id,
          couponCode: couponResponse.data.code,
          couponName: couponResponse.data.name,
          qrCodeLink: couponResponse.data.link_scan_qr_code
        }

        return {
          success: true,
          couponData,
          message: `Bạn nhận được ${prize.name}`
        }
      } else {
        // Nếu API trả về false, tìm prize có type = 'lose' để thay thế
        const losePrize = prizes.find(p => p.type === 'lose')
        return {
          success: false,
          replacementPrize: losePrize,
          message: losePrize ? losePrize.name : 'Chúc bạn may mắn lần sau!'
        }
      }
    } else {
      // Trường hợp này không nên xảy ra vì randomPrizeWithQuantityCheck đã lọc rồi
      // Nhưng để an toàn, tìm prize có type = 'lose' để thay thế
      const losePrize = prizes.find(p => p.type === 'lose')
      return {
        success: false,
        replacementPrize: losePrize,
        message: losePrize ? losePrize.name : 'Chúc bạn may mắn lần sau!'
      }
    }
  } catch (error) {
    console.error('Error getting coupon:', error)
    return {
      success: false,
      message: 'Chúc bạn may mắn lần sau!'
    }
  }
}

// Module xử lý prize type 'spin' - lưu vào VoucherDraw
const saveVoucherDrawForSpinPrize = async (
  userId: number,
  campaignId: number,
  gameId: number,
  couponData: CouponData
): Promise<void> => {
  try {
    // Lấy thông tin user để có số điện thoại
    const user = await userRepo().findOne({ where: { id: userId } })
    const userPhone = user?.phone || ''

    const voucherDraw = new VoucherDraw()
    voucherDraw.userId = userId
    voucherDraw.campaignId = campaignId
    voucherDraw.gameId = gameId
    voucherDraw.phone = userPhone
    voucherDraw.voucherCode = couponData.couponCode
    voucherDraw.storageId = couponData.couponId.toString()
    voucherDraw.createdAt = getNowGMT7()

    await voucherDrawRepo().save(voucherDraw)
    console.log('Đã lưu voucher draw cho spin prize:', couponData.couponCode)
  } catch (error) {
    console.error('Lỗi khi lưu voucher draw cho spin prize:', error)
  }
}

// Module xử lý Game G1
interface G1GameResult {
  status: string
  message: string
  play_turns: number
}

const processG1Game = async (
  user: any,
  game: any,
  userSpin: any,
  campaignId: number,
  gameId: number,
  prizes: any[],
  context?: string
): Promise<G1GameResult> => {
  let couponData = null
  let message = `Chúc bạn may mắn lần sau!`

  // Kiểm tra xem user đã chơi lần nào chưa
  const existingHistory = await spinHistoryRepo()
    .createQueryBuilder('spin_history')
    .where('spin_history.userId = :userId', { userId: user.id })
    .andWhere('spin_history.campaignId = :campaignId', { campaignId })
    .andWhere('spin_history.gameId = :gameId', { gameId })
    .getCount()

  let prize: any
  if (existingHistory === 0) {
    // Lần đầu chơi - bắt buộc trúng voucher100
    const voucher100Prizes = await luckyWheelService.getActivePrizesVoucher100(campaignId, gameId, context)

    if (voucher100Prizes.length > 0) {
      // OPTIMIZATION: Batch query cho voucher100 prizes
      const voucher100PrizeIds = voucher100Prizes.map(p => p.id)
      const voucher100WonCountMap = new Map<number, number>()

      if (voucher100PrizeIds.length > 0) {
        const wonCounts = await spinHistoryRepo()
          .createQueryBuilder('spin_history')
          .select('spin_history.prizeId', 'prizeId')
          .addSelect('COUNT(*)', 'count')
          .where('spin_history.prizeId IN (:...prizeIds)', { prizeIds: voucher100PrizeIds })
          .groupBy('spin_history.prizeId')
          .getRawMany()

        wonCounts.forEach(item => {
          voucher100WonCountMap.set(parseInt(item.prizeId), parseInt(item.count))
        })
      }

      const availableVoucher100 = voucher100Prizes
        .map(v100Prize => {
          const wonCount = voucher100WonCountMap.get(v100Prize.id) || 0
          const remainingQuantity = v100Prize.quantity - wonCount
          return {
            ...v100Prize,
            remainingQuantity
          }
        })
        .filter(p => p.remainingQuantity > 0)

      if (availableVoucher100.length > 0) {
        // Random chọn 1 voucher100 từ danh sách còn số lượng
        const randomIndex = Math.floor(Math.random() * availableVoucher100.length)
        prize = availableVoucher100[randomIndex]
      } else {
        // Nếu voucher100 hết số lượng, dùng logic random như cũ
        prize = randomPrizeWithQuantityCheck(prizes)
      }
    } else {
      // Nếu không có voucher100, dùng logic random như cũ
      prize = randomPrizeWithQuantityCheck(prizes)
    }
  } else {
    // Từ lần thứ 2 trở đi - dùng logic random như cũ
    prize = randomPrizeWithQuantityCheck(prizes)
  }

  if (!prize) {
    throw new NotFoundError('Không có phần thưởng nào', '', 1)
  }

  // Xử lý coupon nếu có bizStorageId
  if (prize.bizStorageId) {
    const couponResult = await processCouponFromBizfly(user, prize, prizes, game, campaignId, gameId)

    if (couponResult.success && couponResult.couponData) {
      couponData = couponResult.couponData
      message = couponResult.message

      // Nếu prize có type 'spin', lưu vào VoucherDraw
      if (prize.type === 'spin') {
        await saveVoucherDrawForSpinPrize(user.id, campaignId, gameId, couponResult.couponData)
      }
    } else {
      if (couponResult.replacementPrize) {
        prize = couponResult.replacementPrize
      }
      message = couponResult.message
    }
  }

  // Lưu vào SpinHistory
  await luckyWheelService.saveSpinHistory(user.id, prize.id || 0, campaignId, gameId, couponData)

  // Trừ lượt chơi
  userSpin.spinCounts -= 1
  userSpin.updatedAt = getNowGMT7()
  await userSpinRepo().save(userSpin)

  return {
    status: 'success',
    message,
    play_turns: userSpin.spinCounts
  }
}

// Module xử lý Game G2
interface G2GameResult {
  status: string
  message: string
  boxes: any[]
  play_turns: number
}

// Helper function to random select a prize from list based on winrate
const selectPrizeByWinrate = (prizes: any[]): any | null => {
  if (prizes.length === 0) return null
  
  const total = prizes.reduce((sum, p) => sum + (p.winrate || 0), 0)
  
  if (total === 0) {
    // Nếu không có winrate, chọn random đều
    const randomIndex = Math.floor(Math.random() * prizes.length)
    return prizes[randomIndex]
  }
  
  let rand = Math.random() * total
  
  for (const prize of prizes) {
    const prizeWinrate = prize.winrate || 0
    if (rand < prizeWinrate) {
      return prize
    }
    rand -= prizeWinrate
  }
  
  // Fallback
  return prizes[prizes.length - 1]
}

const processG2Game = async (
  user: any,
  game: any,
  userSpin: any,
  campaignId: number,
  gameId: number,
  prizes: any[],
  listBoxId: Array<number> = []
): Promise<G2GameResult> => {
  // Cấu hình số box có thể trúng cùng lúc
  const config = {
    MAX_WINNING_BOXES: game.maxWinPrize || 0, // Mặc định random box trúng trong 3 box mở
  }
  console.log('prizes', prizes)

  // Lọc prizes theo type
  const winPrizes = prizes.filter(p => p.type !== 'lose')
  const losePrizes = prizes.filter(p => p.type === 'lose')

  if (winPrizes.length === 0 && losePrizes.length === 0) {
    throw new NotFoundError('Không có phần thưởng nào', '', 1)
  }

  // Tạo danh sách 9 boxes với reward
  const boxes = Array(9).fill(null)
  const sentBoxIds = listBoxId.length > 0 ? listBoxId : []
  console.log('sentBoxIds', [...sentBoxIds])

  // Xác định số box trúng dựa trên MAX_WINNING_BOXES
  let actualWinningBoxCount = 0
  let predefinedWinningPrize = null

  if (config.MAX_WINNING_BOXES === 0) {
    // Random prize giống G1 để xác định có trúng hay không
    const randomizedPrize = randomPrizeWithQuantityCheck(prizes)
    console.log('randomizedPrize', randomizedPrize)
    if (randomizedPrize && ['voucher', 'spin'].includes(randomizedPrize.type)) {
      actualWinningBoxCount = Math.min(1, sentBoxIds.length) // Tối đa 1 box trúng khi random
    }
    predefinedWinningPrize = randomizedPrize // Lưu prize đã random để dùng cho winning box
  } else {
    // Bắt buộc có số box trúng theo MAX_WINNING_BOXES
    actualWinningBoxCount = Math.min(config.MAX_WINNING_BOXES, sentBoxIds.length)
  }
  console.log('actualWinningBoxCount', actualWinningBoxCount)

  // Chọn random winning boxes từ sentBoxIds
  const winningBoxes = []
  if (actualWinningBoxCount > 0) {
    const remainingSentBoxes = [...sentBoxIds]
    for (let i = 0; i < actualWinningBoxCount; i++) {
      const randomIndex = Math.floor(Math.random() * remainingSentBoxes.length)
      winningBoxes.push(remainingSentBoxes.splice(randomIndex, 1)[0])
    }
    console.log('winningBoxes', [...winningBoxes])
  }

  // Lưu trữ các prize đã sử dụng để tránh lặp lại
  const usedPrizeIds = new Set()
  console.log('sentBoxIds', sentBoxIds)

  // Fill các box được gửi lên
  for (const boxId of sentBoxIds) {
    const boxIndex = boxId - 1
    if (winningBoxes.includes(boxId)) {
      // Box trúng - sử dụng prize đã được định trước hoặc random từ winPrizes
      let prize: any
      console.log('predefinedWinningPrize', predefinedWinningPrize)
      if (predefinedWinningPrize && winningBoxes[0] === boxId) {
        // Sử dụng prize đã random từ MAX_WINNING_BOXES = 0 cho box trúng đầu tiên
        prize = predefinedWinningPrize
      } else {
        // Cho các trường hợp khác, random từ winPrizes còn số lượng theo winrate
        const availableWinPrizes = winPrizes.filter(p =>
          !usedPrizeIds.has(p.id) && p.remainingQuantity > 0
        )
        const prizeToUse = availableWinPrizes.length > 0 ? availableWinPrizes : winPrizes

        // Sử dụng hàm random theo winrate thay vì random đều
        prize = selectPrizeByWinrate(prizeToUse)
      }

      boxes[boxIndex] = {
        id: boxId,
        reward: {
          reward_id: prize.id,
          name: prize.name,
          image: prize.imageSlug ? prize.imageSlug : (prize.image ? prize.image.split('/')[prize.image.split('/').length - 1] : '')
        },
        prizeData: prize // Lưu thông tin prize để xử lý coupon
      }
      usedPrizeIds.add(prize.id)
    } else {
      // Box không trúng trong danh sách gửi lên - dùng lose prizes
      if (losePrizes.length > 0) {
        const availableLosePrizes = losePrizes.filter(p => !usedPrizeIds.has(p.id))
        const prizeToUse = availableLosePrizes.length > 0 ? availableLosePrizes : losePrizes
        const prizeIndex = Math.floor(Math.random() * prizeToUse.length)
        const prize = prizeToUse[prizeIndex]

        boxes[boxIndex] = {
          id: boxId,
          reward: {
            reward_id: prize.id,
            name: prize.name,
            image: prize.imageSlug ? prize.imageSlug : (prize.image ? prize.image.split('/')[prize.image.split('/').length - 1] : '')
          }
        }
        usedPrizeIds.add(prize.id)
      } else {
        // Nếu không có lose prizes, dùng mặc định
        boxes[boxIndex] = {
          id: boxId,
          reward: {
            reward_id: 0,
            name: 'Chúc bạn may mắn lần sau',
            image: ''
          }
        }
      }
    }
  }

  // Fill các box còn lại (không được gửi lên) bằng tất cả prizes (win + lose) trừ đã dùng
  const allPrizes = [...winPrizes, ...losePrizes]
  const availablePrizesForRemaining = allPrizes.filter(p => !usedPrizeIds.has(p.id))
  const prizesToUseForRemaining = availablePrizesForRemaining.length > 0 ? availablePrizesForRemaining : allPrizes

  // Shuffle prizes để random thứ tự
  const shufflePrizes = (array: typeof allPrizes) => {
    const shuffled = [...array]
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
    }
    return shuffled
  }

  const shuffledPrizes = shufflePrizes(prizesToUseForRemaining)
  let remainingPrizeIndex = 0

  for (let i = 1; i <= 9; i++) {
    if (boxes[i - 1] === null) {
      // Box chưa được fill - lấy prize với thứ tự đã shuffle
      const prize = shuffledPrizes[remainingPrizeIndex % shuffledPrizes.length]

      boxes[i - 1] = {
        id: i,
        reward: {
          reward_id: prize.id,
          name: prize.name,
          image: prize.imageSlug ? prize.imageSlug : (prize.image ? prize.image.split('/')[prize.image.split('/').length - 1] : '')
        }
      }
      remainingPrizeIndex++
    }
  }

  // Xử lý coupon cho từng winning box
  const couponDataList = []
  let message = 'Chúc bạn may mắn lần sau!'
  const status = 'success'

  console.log('winningBoxes', winningBoxes)
  console.log('boxes', boxes)

  if (winningBoxes.length > 0) {
    let isGetPrize = false
    const listErrorBox = []

    for (const winningBoxId of winningBoxes) {
      const winningBox = boxes.find(box => box.id === winningBoxId && box.prizeData)
      if (winningBox && winningBox.prizeData) {
        let winningPrize = winningBox.prizeData
        console.log('winningPrize', winningPrize)
        let couponData = null

        if (['voucher', 'spin'].includes(winningPrize.type) && winningPrize.bizStorageId) {
          const couponResult = await processCouponFromBizfly(user, winningPrize, losePrizes, game, campaignId, gameId)

          if (couponResult.success && couponResult.couponData) {
            couponData = couponResult.couponData
            couponDataList.push(couponData)
            isGetPrize = true

            // Nếu prize có type 'spin', lưu vào VoucherDraw
            if (winningPrize.type === 'spin') {
              await saveVoucherDrawForSpinPrize(user.id, campaignId, gameId, couponResult.couponData)
            }
          } else {
            // Nếu API trả về false, tìm prize có type = 'lose' để thay thế
            listErrorBox.push(winningBoxId)
            const losePrize = losePrizes[0]
            if (losePrize) {
              winningPrize = losePrize
              winningBox.reward.reward_id = losePrize.id
              winningBox.reward.name = losePrize.name
              winningBox.reward.image = losePrize.imageSlug ? losePrize.imageSlug : (losePrize.image ? losePrize.image.split('/')[losePrize.image.split('/').length - 1] : '')
            } else {
              winningBox.reward = {
                reward_id: 0,
                name: 'Chúc bạn may mắn lần sau',
                image: ''
              }
            }
          }
        }

        // Lưu vào SpinHistory cho mỗi box trúng
        await luckyWheelService.saveSpinHistory(user.id, winningPrize.id || 0, campaignId, gameId, couponData)
      }
    }

    let newWinningBoxes = [...winningBoxes]
    if (listErrorBox.length > 0) {
      newWinningBoxes = newWinningBoxes.filter(box => !listErrorBox.includes(box))
    }
    const boxNumbers = newWinningBoxes.join(', ')
    if (isGetPrize == true) {
      message = `Bạn đã mở hộp quà số ${boxNumbers} và nhận được voucher!`
    } else {
      message = `Bạn đã mở hộp quà số ${boxNumbers} nhưng đã hết voucher này!`
    }
  }

  // Lưu lịch sử chơi trường hợp không trúng
  console.log('predefinedWinningPrize', predefinedWinningPrize)
  if (predefinedWinningPrize && predefinedWinningPrize.type == 'lose') {
    await luckyWheelService.saveSpinHistory(user.id, predefinedWinningPrize.id || 0, campaignId, gameId, null)
  }

  // Trừ lượt chơi
  userSpin.spinCounts -= 1
  userSpin.updatedAt = getNowGMT7()
  await userSpinRepo().save(userSpin)

  // Loại bỏ prizeData khỏi response
  const responseBoxes = boxes.map(box => ({
    id: box.id,
    reward: box.reward
  }))

  return {
    status,
    message,
    boxes: responseBoxes,
    play_turns: userSpin.spinCounts
  }
}

// Module xử lý khi hết lượt chơi
interface NoTurnsResult {
  status: string
  message: string
  received_rewards?: any[]
  boxes?: any[]
  share_remaining: number
}

const processNoTurns = async (
  user: any,
  game: any,
  userSpin: any,
  campaignId: number,
  gameId: number,
  gameIdSlug: string,
  context?: string
): Promise<NoTurnsResult> => {
  // Kiểm tra share_remaining
  const now = getNowGMT7()
  const shareRemaining = userSpin.lastRequestFacebook && isSameDay(userSpin.lastRequestFacebook, now) ? 0 : 1

  if (gameIdSlug === 'g1') {
    // Hết lượt chơi G1 - lấy danh sách phần thưởng đã nhận
    const receivedRewards = await spinHistoryRepo()
      .createQueryBuilder('spin_histories')
      .leftJoinAndSelect('spin_histories.prize', 'prize')
      .where('spin_histories.userId = :userId', { userId: user.id })
      .andWhere('spin_histories.campaignId = :campaignId', { campaignId })
      .andWhere('spin_histories.gameId = :gameId', { gameId })
      .getMany()

    const rewards = receivedRewards.map(history => ({
      id: history.prize.id,
      name: history.voucherName || history.prize.name
    }))

    return {
      status: 'no_turns',
      message: 'Bạn đã hết lượt chơi hôm nay',
      received_rewards: rewards,
      share_remaining: shareRemaining
    }
  }

  if (gameIdSlug === 'g2') {
    // Hết lượt chơi G2 - trả về tất cả boxes
    const prizes = await getPrizesWithRemainingQuantity(campaignId, game.id, context)
    const winPrizes = prizes.filter(p => p.type !== 'lose')
    const losePrizes = prizes.filter(p => p.type === 'lose')
    const allPrizes = [...winPrizes, ...losePrizes]

    // Shuffle prizes để random thứ tự
    const shufflePrizes = (array: typeof allPrizes) => {
      const shuffled = [...array]
      for (let i = shuffled.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
      }
      return shuffled
    }

    const shuffledAllPrizes = shufflePrizes(allPrizes)

    const boxes = []
    for (let i = 1; i <= 9; i++) {
      const prizeIndex = (i - 1) % shuffledAllPrizes.length
      const prize = shuffledAllPrizes[prizeIndex]
      boxes.push({
        id: i,
        reward: {
          reward_id: prize.id,
          name: prize.name,
          image: prize.image
        }
      })
    }

    return {
      status: 'no_turns',
      message: 'Bạn đã hết lượt chơi hôm nay',
      boxes,
      share_remaining: shareRemaining
    }
  }

  // Default fallback
  return {
    status: 'no_turns',
    message: 'Bạn đã hết lượt chơi hôm nay',
    share_remaining: shareRemaining
  }
}

/**
 * 1. GET /api/game/init
 * Initialize game, preload user data and game UI
 */
export const initGame = async (req: AuthorizedUserRequest, gameIdSlug: string) => {
  const { user } = req.authorizedUser
  const gameId = convertGameIdSlugToNumber(gameIdSlug)

  if (!gameId) {
    throw new NotFoundError('Game không tồn tại', '', 1)
  }

  // Lấy thông tin game để có campaignId
  const game = await gameRepo().findOne({
    where: { id: gameId }
  })

  if (!game) {
    throw new NotFoundError('Game không tồn tại', '', 1)
  }

  const campaignId = game.campaignId

  // Lấy thông tin UserSpin
  let userSpin = await userSpinRepo().findOne({
    where: { userId: user.id, campaignId, gameId }
  })

  // Nếu chưa có UserSpin, tạo mới với số lượt mặc định
  if (!userSpin) {
    const now = getNowGMT7()
    userSpin = new UserSpin()
    userSpin.userId = user.id
    userSpin.campaignId = campaignId
    userSpin.gameId = gameId
    userSpin.points = 0
    userSpin.createdAt = now
    userSpin.updatedAt = now
    userSpin.lastRequest = now
    if(gameIdSlug === 'g1'){
      userSpin.spinCounts = 2 // Số lượt chơi mặc định mỗi ngày game G1
    }else{
      userSpin.spinCounts = 1 // Số lượt chơi mặc định mỗi ngày game G2
    }
    await userSpinRepo().save(userSpin)
  } else {
    const now = getNowGMT7()
    if(userSpin.spinCounts == 0){

        // Kiểm tra nếu lastRequest là ngày hôm trước thì cộng thêm lượt chơi cho ngày mới
        if (!userSpin.lastRequest || !isSameDay(userSpin.lastRequest, now)) {
          if(gameIdSlug === 'g1'){
            userSpin.spinCounts += 2
          }else{
            userSpin.spinCounts += 1
          }
            userSpin.lastRequest = now
        }

        userSpin.updatedAt = now
        await userSpinRepo().save(userSpin)
    }
    if(gameIdSlug === 'g1' && userSpin.spinCounts == 1){
      userSpin.spinCounts = 2
      userSpin.updatedAt = now
      await userSpinRepo().save(userSpin)
    }
  }

  return {
    status: 'success',
    user: {
      id: user.id,
      name: user.name
    },
    play_turns: userSpin.spinCounts
  }
}

/**
 * 2. GET /api/game/play
 * Start the game and get vouchers that will fall
 */
export const playGame = async (req: AuthorizedUserRequest, gameIdSlug: string) => {
  const { user } = req.authorizedUser
  const gameId = convertGameIdSlugToNumber(gameIdSlug)

  if (!gameId) {
    throw new NotFoundError('Game không tồn tại', '', 1)
  }

  // Lấy thông tin game để có campaignId
  const game = await gameRepo().findOne({
    where: { id: gameId }
  })

  if (!game) {
    throw new NotFoundError('Game không tồn tại', '', 1)
  }

  const campaignId = game.campaignId

  // Lấy thông tin UserSpin
  const userSpin = await userSpinRepo().findOne({
    where: { userId: user.id, campaignId, gameId }
  })

  if (!userSpin) {
    throw new NotFoundError('Vui lòng khởi tạo game trước', '', 1)
  }

  // Lấy danh sách phần quà (vouchers)
  const prizes = await prizeRepo().find({
    where: { campaignId, gameId, active: 1 },
    order: { displayOrder: 'ASC' }
  })

  const vouchers = prizes.map(prize => ({
    id: prize.bizStorageId || prize.id,
    value: prize.name,
    image: prize.image
  }))

  return {
    status: 'success',
    play_turns: userSpin.spinCounts,
    vouchers
  }
}

/**
 * 3. POST /api/game/claim
 * Claim a voucher by tapping it
 */
export const claimVoucher = async (req: AuthorizedUserRequest, gameIdSlug: string, listBoxId: Array<number> = [], context?: string) => {
  const { user } = req.authorizedUser
  const gameId = convertGameIdSlugToNumber(gameIdSlug)

  if (!gameId) {
    throw new NotFoundError('Game không tồn tại', '', 1)
  }

  const game = await gameRepo().findOne({
    where: { id: gameId }
  })

  if (!game) {
    throw new NotFoundError('Game không tồn tại', '', 1)
  }

  const campaignId = game.campaignId

  // Lấy thông tin UserSpin
  const userSpin = await userSpinRepo().findOne({
    where: { userId: user.id, campaignId, gameId }
  })

  if (!userSpin) {
    throw new NotFoundError('Vui lòng khởi tạo game trước', '', 1)
  }

  // Nếu còn lượt chơi
  if (userSpin.spinCounts > 0) {
    const prizes = await getPrizesWithRemainingQuantity(campaignId, game.id, context)

    if (gameIdSlug === 'g1') {
      // Sử dụng module xử lý Game G1
      return processG1Game(user, game, userSpin, campaignId, gameId, prizes, context)
    }
    else if (gameIdSlug === 'g2') {
      // Sử dụng module xử lý Game G2
      return processG2Game(user, game, userSpin, campaignId, gameId, prizes, listBoxId)
    }
  } else {
    // Sử dụng module xử lý khi hết lượt chơi
    return processNoTurns(user, game, userSpin, campaignId, gameId, gameIdSlug, context)
  }
}

/**
 * 4. POST /api/game/share
 * Share to receive 1 extra play turn (1 time per day)
 */
export const shareForExtraTurn = async (req: AuthorizedUserRequest, gameIdSlug: string, platform: string) => {
  const { user } = req.authorizedUser
  const gameId = convertGameIdSlugToNumber(gameIdSlug)

  if (!gameId) {
    throw new NotFoundError('Game không tồn tại', '', 1)
  }

  // Lấy thông tin game để có campaignId
  const game = await gameRepo().findOne({
    where: { id: gameId }
  })

  if (!game) {
    throw new NotFoundError('Game không tồn tại', '', 1)
  }

  const campaignId = game.campaignId

  // Lấy thông tin UserSpin
  const userSpin = await userSpinRepo().findOne({
    where: { userId: user.id, campaignId, gameId }
  })

  if (!userSpin) {
    throw new NotFoundError('Vui lòng khởi tạo game trước', '', 1)
  }

  const now = getNowGMT7()
   // Kiểm tra đã share facebook hôm nay chưa
  if(platform === 'facebook'){
    if (userSpin.lastRequestFacebook && isSameDay(userSpin.lastRequestFacebook, now)) {
        return {
            status: 'already_shared',
            message: 'Bạn đã chia sẻ và nhận lượt hôm nay rồi'
        }
    }
    // Cộng thêm 1 lượt chơi
    userSpin.spinCounts += 1
    userSpin.lastRequestFacebook = now
    userSpin.updatedAt = now
    await userSpinRepo().save(userSpin)

    return {
        status: 'success',
        message: 'Bạn đã nhận thêm 1 lượt chơi!',
        play_turns: userSpin.spinCounts
      }
  }else{
    return {
        status: 'error',
        message: 'Không hỗ trợ platform này'
    }
  }
}

// API 1: Lấy SpinHistory theo user, game_id và campaign_id
export const getSpinHistoryByUserGameCampaign = async (userId: number, gameId: number) => {
  return spinHistoryRepo()
    .createQueryBuilder('spin_histories')
    .leftJoinAndSelect('spin_histories.prize', 'prize')
    .where('spin_histories.userId = :userId', { userId })
    .andWhere('spin_histories.gameId = :gameId', { gameId })
    .orderBy('spin_histories.spinTime', 'DESC')
    .getMany()
}

// API 2: Lấy SpinHistory theo user, game_id và campaign_id, chỉ những record có voucherCode và voucherName
export const getSpinHistoryWithVoucherByUserCampaign = async (userId: number) => {
  const query = spinHistoryRepo()
    .createQueryBuilder('spin_histories')
    .leftJoinAndSelect('spin_histories.prize', 'prize')
    .where('spin_histories.userId = :userId', { userId })
    .andWhere('spin_histories.voucherCode IS NOT NULL')
    .andWhere('spin_histories.voucherName IS NOT NULL')
    .andWhere('spin_histories.voucherCode != ""')
    .andWhere('spin_histories.voucherName != ""')

  return query.orderBy('spin_histories.spinTime', 'DESC').getMany()
}
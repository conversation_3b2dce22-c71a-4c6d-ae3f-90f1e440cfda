import express from 'express'
import { gameG1API } from '@/api'
import { authenticate } from '@/middlewares/auth'

// Game G1 router
const gameG1Router = express.Router()

// GET /api/game/:slug/init
gameG1Router.get('/:slug/init', authenticate, gameG1API.initGameHandler)

// GET /api/game/:slug/play  
gameG1Router.get('/:slug/play', authenticate, gameG1API.playGameHandler)

// POST /api/game/:slug/claim
gameG1Router.post('/:slug/claim', authenticate, gameG1API.claimVoucherHandler)

// POST /api/game/:slug/share
gameG1Router.post('/:slug/share', authenticate, gameG1API.shareForExtraTurnHandler)

gameG1Router.get('/:slug/history', authenticate, gameG1API.getSpinHistoryByGameCampaignHandler)

gameG1Router.get('/voucher-storage', authenticate, gameG1API.getSpinHistoryWithVoucherHandler)

// Export router
const router = express.Router()
router.use('/game', gameG1Router)

export default router 